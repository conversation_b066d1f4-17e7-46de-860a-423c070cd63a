import { NextRequest, NextResponse } from 'next/server';
import { extractLinktreeData } from '@/lib/linktreeExtractor';

export async function POST(req: NextRequest) {
  try {
    const { url } = await req.json();

    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 });
    }

    const extractResult = await extractLinktreeData(url);

    return NextResponse.json(extractResult);
  } catch (error) {
    console.error('Error extracting Linktree data:', error);
    return NextResponse.json({ error: 'Failed to extract Linktree data' }, { status: 500 });
  }
}
