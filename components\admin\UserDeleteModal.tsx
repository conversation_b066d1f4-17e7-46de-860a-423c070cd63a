"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalD<PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  ModalClose,
} from "@/components/ui/modal";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import {
  AlertTriangle,
  User,
  Database,
  Trash2,
  Loader2,
  Clock,
  Shield,
  CheckCircle,
} from "lucide-react";

export interface UserInfo {
  username: string;
  displayName?: string;
  lastUpdated?: string;
  recordCount?: number;
  isOnline?: boolean;
}

interface UserDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: UserInfo | null;
  onDeleteSuccess: (deletedUser: UserInfo) => void;
}

interface DeleteResponse {
  success: boolean;
  message?: string;
  error?: string;
  deletedUser?: {
    username: string;
    displayName: string;
    recordCount: number;
  };
}

export default function UserDeleteModal({
  isOpen,
  onClose,
  user,
  onDeleteSuccess,
}: UserDeleteModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmationText, setConfirmationText] = useState("");
  const [step, setStep] = useState<"confirm" | "deleting" | "success">(
    "confirm"
  );
  const { toast } = useToast();

  const expectedConfirmation = user ? `DELETE ${user.username}` : "";
  const isConfirmationValid = confirmationText === expectedConfirmation;

  const handleDelete = async () => {
    if (!user || !isConfirmationValid) return;

    setIsDeleting(true);
    setStep("deleting");

    try {
      const response = await fetch(`/api/users/${user.username}`, {
        method: "DELETE",
      });

      const data: DeleteResponse = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.error || "Failed to delete user");
      }

      // Show success step briefly
      setStep("success");

      toast({
        title: "User Deleted Successfully",
        description:
          data.message || `User ${user.username} has been permanently deleted`,
      });

      // Call success callback with updated user info
      const deletedUserInfo: UserInfo = {
        username: data.deletedUser?.username || user.username,
        displayName: data.deletedUser?.displayName || user.displayName,
        recordCount: data.deletedUser?.recordCount || user.recordCount,
        isOnline: false,
      };

      onDeleteSuccess(deletedUserInfo);

      // Close modal after brief delay
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (error) {
      console.error("Error deleting user:", error);

      toast({
        title: "Deletion Failed",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      });

      setStep("confirm");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    if (isDeleting) return; // Prevent closing during deletion

    setStep("confirm");
    setConfirmationText("");
    setIsDeleting(false);
    onClose();
  };

  const handleConfirmationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfirmationText(e.target.value);
  };

  if (!user) return null;

  return (
    <Modal open={isOpen} onOpenChange={handleClose}>
      <ModalContent size="md" showCloseButton={!isDeleting}>
        {step === "confirm" && (
          <>
            <ModalHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-destructive/10 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-destructive" />
                </div>
                <div>
                  <ModalTitle className="text-destructive">
                    Delete User Account
                  </ModalTitle>
                  <ModalDescription>
                    This action cannot be undone. All user data will be
                    permanently deleted.
                  </ModalDescription>
                </div>
              </div>
            </ModalHeader>

            <div className="space-y-6">
              {/* User Details */}
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-3 mb-3">
                  <User className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium">User Details</span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Username:</span>
                    <span className="font-medium">@{user.username}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Display Name:</span>
                    <span>{user.displayName || user.username}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Status:</span>
                    <Badge variant={user.isOnline ? "default" : "secondary"}>
                      {user.isOnline ? "Online" : "Offline"}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Last Updated:</span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {user.lastUpdated
                        ? new Date(user.lastUpdated).toLocaleDateString()
                        : "Unknown"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Data Impact */}
              <div className="p-4 bg-destructive/5 border border-destructive/20 rounded-lg">
                <div className="flex items-center gap-3 mb-3">
                  <Database className="h-5 w-5 text-destructive" />
                  <span className="font-medium text-destructive">
                    Data Impact
                  </span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Total Records:</span>
                    <Badge variant="destructive">{user.recordCount || 0}</Badge>
                  </div>
                  <p className="text-muted-foreground text-xs">
                    All user data including profile, links, gallery images,
                    reviews, team members, services, and settings will be
                    permanently deleted.
                  </p>
                </div>
              </div>

              {/* Security Warning */}
              <div className="p-4 bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                <div className="flex items-center gap-3 mb-2">
                  <Shield className="h-5 w-5 text-amber-600" />
                  <span className="font-medium text-amber-800 dark:text-amber-200">
                    Security Confirmation Required
                  </span>
                </div>
                <p className="text-sm text-amber-700 dark:text-amber-300 mb-3">
                  To confirm deletion, type{" "}
                  <code className="bg-amber-100 dark:bg-amber-900 px-1 rounded">
                    {expectedConfirmation}
                  </code>{" "}
                  below:
                </p>
                <input
                  type="text"
                  value={confirmationText}
                  onChange={handleConfirmationChange}
                  placeholder={expectedConfirmation}
                  className="w-full px-3 py-2 border border-amber-300 dark:border-amber-700 rounded-md bg-white dark:bg-amber-950/50 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500"
                  disabled={isDeleting}
                />
              </div>
            </div>

            <ModalFooter>
              <ModalClose asChild>
                <Button variant="outline" disabled={isDeleting}>
                  Cancel
                </Button>
              </ModalClose>
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={!isConfirmationValid || isDeleting}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete User Permanently
              </Button>
            </ModalFooter>
          </>
        )}

        {step === "deleting" && (
          <>
            <ModalHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-destructive/10 rounded-full">
                  <Loader2 className="h-6 w-6 text-destructive animate-spin" />
                </div>
                <div>
                  <ModalTitle>Deleting User...</ModalTitle>
                  <ModalDescription>
                    Please wait while we permanently delete the user account and
                    all associated data.
                  </ModalDescription>
                </div>
              </div>
            </ModalHeader>

            <div className="py-8">
              <div className="flex flex-col items-center gap-4">
                <div className="relative">
                  <div className="w-16 h-16 border-4 border-destructive/20 border-t-destructive rounded-full animate-spin"></div>
                  <Trash2 className="absolute inset-0 m-auto h-6 w-6 text-destructive" />
                </div>
                <div className="text-center">
                  <p className="font-medium">Deleting @{user.username}</p>
                  <p className="text-sm text-muted-foreground">
                    Removing {user.recordCount || 0} records...
                  </p>
                </div>
              </div>
            </div>
          </>
        )}

        {step === "success" && (
          <>
            <ModalHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-full">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <ModalTitle className="text-green-700 dark:text-green-400">
                    User Deleted Successfully
                  </ModalTitle>
                  <ModalDescription>
                    The user account and all associated data have been
                    permanently removed.
                  </ModalDescription>
                </div>
              </div>
            </ModalHeader>

            <div className="py-6">
              <div className="flex flex-col items-center gap-4">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <div className="text-center">
                  <p className="font-medium">
                    @{user.username} has been deleted
                  </p>
                  <p className="text-sm text-muted-foreground">
                    All {user.recordCount || 0} records have been removed
                  </p>
                </div>
              </div>
            </div>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
