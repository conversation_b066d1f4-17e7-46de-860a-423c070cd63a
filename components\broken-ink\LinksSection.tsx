"use client";

import React, { useCallback, useEffect, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { getIconComponent } from "@/lib/iconUtils";
import { UserProfile } from "@/types/user";
import { transformLinksData } from "@/lib/brokenInkUtils";
import { cn } from "@/lib/utils";

interface LinksSectionProps {
  profile: UserProfile;
}

const LinksSection = ({ profile }: LinksSectionProps) => {
  const linksData = transformLinksData(profile);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    containScroll: "trimSnaps",
    dragFree: false,
    loop: false,
    skipSnaps: false,
  });

  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);

    // Simulate loading
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, [emblaApi, onSelect]);

  // Enhanced link click handler
  const handleLinkClick = useCallback(
    async (url: string, event: React.MouseEvent) => {
      try {
        if (
          url.startsWith("http") ||
          url.startsWith("mailto:") ||
          url.startsWith("tel:")
        ) {
          window.open(url, "_blank", "noopener,noreferrer");
        } else if (url.startsWith("#")) {
          event.preventDefault();
          const element = document.querySelector(url);
          if (element) {
            element.scrollIntoView({
              behavior: "smooth",
              block: "start",
              inline: "nearest",
            });
          }
        }
      } catch (error) {
        console.error("Error opening link:", error);
      }
    },
    []
  );

  // Don't render if no links and not loading
  if (!isLoading && linksData.links.length === 0) {
    return null;
  }

  return (
    <section className="pb-16 sm:pb-24 bg-black max-w-7xl mx-auto" id="links">
      <div className="px-4 sm:px-6 lg:px-8 flex flex-col items-center">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            {/* <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {linksData.title}
          </h2> */}
            {linksData.description && (
              <p className="text-gray-300 text-lg max-w-2xl mx-auto">
                {linksData.description}
              </p>
            )}
          </div>

          {/* Horizontal Scroll Carousel for All Screen Sizes */}
          <div className="relative">
            <div className="overflow-hidden" ref={emblaRef}>
              <div className="flex gap-4 touch-pan-y">
                {linksData.links.map((link, index) => {
                  const IconComponent = getIconComponent(link.iconName);
                  return (
                    <div
                      key={index}
                      className="flex-none w-[320px] sm:w-[360px] md:w-[380px] p-2"
                    >
                      <LinkCard
                        icon={<IconComponent className="h-6 w-6 text-white" />}
                        title={link.text}
                        description={link.description}
                        onClick={(e) => handleLinkClick(link.url, e)}
                        isLoading={isLoading}
                      />
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Scroll Indicators */}
            {!isLoading && linksData.links.length > 1 && (
              <div className="flex justify-center mt-6 gap-2">
                {linksData.links.map((_, index) => (
                  <button
                    key={index}
                    className={cn(
                      "w-2 h-2 rounded-full transition-all duration-200",
                      index === selectedIndex
                        ? "bg-white"
                        : "bg-white/30 hover:bg-white/50"
                    )}
                    onClick={() => emblaApi?.scrollTo(index)}
                    aria-label={`Ir para link ${index + 1}`}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

// LinkCard Component
interface LinkCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  onClick: (event: React.MouseEvent) => void;
  isLoading?: boolean;
}

const LinkCard: React.FC<LinkCardProps> = ({
  icon,
  title,
  onClick,
  isLoading = false,
}) => {
  if (isLoading) {
    return (
      <div className="w-full animate-pulse">
        <div className="rounded-3xl bg-gray-800/50 backdrop-blur-3xl w-full p-6 ring-1 ring-black/10">
          <div className="flex items-center gap-4">
            <div className="bg-gray-700/50 p-4 rounded-3xl w-14 h-14 flex-shrink-0"></div>
            <div className="space-y-2 flex-1">
              <div className="h-5 bg-gray-700/50 rounded w-3/4"></div>
              <div className="h-4 bg-gray-700/50 rounded w-full"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <button
      className="group relative flex items-center justify-between rounded-3xl bg-custom backdrop-blur-3xl w-full p-6 ring-1 ring-black/10 transition-all hover:ring-white/20 hover:scale-[1.02] active:scale-[0.98] text-left"
      onClick={onClick}
    >
      <div className="flex items-center gap-4 w-full">
        <div className="bg-black/40 p-4 rounded-3xl transition-all group-hover:bg-black/60 flex-shrink-0">
          {icon}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-white text-lg font-semibold mb-1">{title}</h3>
        </div>
      </div>
    </button>
  );
};

export default LinksSection;
